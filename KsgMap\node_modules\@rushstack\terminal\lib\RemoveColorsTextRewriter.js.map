{"version": 3, "file": "RemoveColorsTextRewriter.js", "sourceRoot": "", "sources": ["../src/RemoveColorsTextRewriter.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,6CAA0C;AAC1C,iDAAsE;AAEtE,IAAK,KAOJ;AAPD,WAAK,KAAK;IACR,2DAA2D;IAC3D,mCAAK,CAAA;IACL,sCAAsC;IACtC,uDAAe,CAAA;IACf,kDAAkD;IAClD,iDAAY,CAAA;AACd,CAAC,EAPI,KAAK,KAAL,KAAK,QAOT;AAOD;;;;;;;;;GASG;AACH,MAAa,wBAAyB,SAAQ,2BAAY;IACjD,UAAU;QACf,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,KAAK,EAAoC,CAAC;IACnF,CAAC;IAEM,OAAO,CAAC,YAA+B,EAAE,IAAY;QAC1D,MAAM,KAAK,GAAmC,YAA8C,CAAC;QAE7F,6CAA6C;QAC7C,EAAE;QACF,mDAAmD;QACnD,EAAE;QACF,MAAM,GAAG,GAAW,MAAM,CAAC;QAE3B,IAAI,MAAM,GAAW,EAAE,CAAC;QACxB,IAAI,KAAK,GAAW,CAAC,CAAC;QAEtB,OAAO,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC;gBACrC,iEAAiE;gBAEjE,MAAM,QAAQ,GAAW,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAClD,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;oBACjB,kEAAkE;oBAClE,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBAChC,MAAM;gBACR,CAAC;gBAED,yCAAyC;gBACzC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBAE1C,uCAAuC;gBACvC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;gBACnB,KAAK,GAAG,QAAQ,GAAG,CAAC,CAAC;gBACrB,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,eAAe,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACN,gFAAgF;gBAEhF,yCAAyC;gBACzC,MAAM,CAAC,GAAW,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9B,EAAE,KAAK,CAAC;gBACR,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC;gBAElB,IAAI,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,eAAe,EAAE,CAAC;oBAC/C,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;wBACd,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,YAAY,CAAC;oBACxC,CAAC;yBAAM,CAAC;wBACN,uDAAuD;wBACvD,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;wBACvB,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;wBAClB,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC;oBACjC,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,qCAAqC;oBAErC,0EAA0E;oBAC1E,MAAM,IAAI,GAAW,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBACrC,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC;wBAC/B,MAAM,IAAI,uBAAU,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;wBAC/C,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;wBAClB,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC;oBACjC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,YAA+B;QAC1C,MAAM,KAAK,GAAmC,YAA8C,CAAC;QAE7F,MAAM,MAAM,GAAW,KAAK,CAAC,MAAM,CAAC;QACpC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;QAClB,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AA5ED,4DA4EC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport { AnsiEscape } from './AnsiEscape';\nimport { TextRewriter, type TextRewriterState } from './TextRewriter';\n\nenum State {\n  // <PERSON>uffer is empty, and we're looking for the ESC character\n  Start,\n  // We're looking for the '[' character\n  AwaitingBracket,\n  // We're reading the codes after the '[' character\n  ReadingCodes\n}\n\ninterface IRemoveColorsTextRewriterState extends TextRewriterState {\n  buffer: string;\n  parseState: State;\n}\n\n/**\n * For use with {@link TextRewriterTransform}, this rewriter removes ANSI escape codes\n * including colored text.\n *\n * @remarks\n * The implementation also removes other ANSI escape codes such as cursor positioning.\n * The specific set of affected codes may be adjusted in the future.\n *\n * @public\n */\nexport class RemoveColorsTextRewriter extends TextRewriter {\n  public initialize(): TextRewriterState {\n    return { buffer: '', parseState: State.Start } as IRemoveColorsTextRewriterState;\n  }\n\n  public process(unknownState: TextRewriterState, text: string): string {\n    const state: IRemoveColorsTextRewriterState = unknownState as IRemoveColorsTextRewriterState;\n\n    // We will be matching AnsiEscape._csiRegExp:\n    //\n    //  /\\x1b\\[([\\x30-\\x3f]*[\\x20-\\x2f]*[\\x40-\\x7e])/gu\n    //\n    const ESC: string = '\\x1b';\n\n    let result: string = '';\n    let index: number = 0;\n\n    while (index < text.length) {\n      if (state.parseState === State.Start) {\n        // The buffer is empty, which means we haven't found anything yet\n\n        const csiIndex: number = text.indexOf(ESC, index);\n        if (csiIndex < 0) {\n          // We reached the end of \"text\" without finding another CSI prefix\n          result += text.substring(index);\n          break;\n        }\n\n        // Append everything up to the CSI prefix\n        result += text.substring(index, csiIndex);\n\n        // Save the partial match in the buffer\n        state.buffer = ESC;\n        index = csiIndex + 1;\n        state.parseState = State.AwaitingBracket;\n      } else {\n        // The buffer has characters, which means we started matching a partial sequence\n\n        // Read another character into the buffer\n        const c: string = text[index];\n        ++index;\n        state.buffer += c;\n\n        if (state.parseState === State.AwaitingBracket) {\n          if (c === '[') {\n            state.parseState = State.ReadingCodes;\n          } else {\n            // Failed to match, so append the buffer and start over\n            result += state.buffer;\n            state.buffer = '';\n            state.parseState = State.Start;\n          }\n        } else {\n          // state.state === State.ReadingCodes\n\n          // Stop when we reach any character that is not [\\x30-\\x3f] or [\\x20-\\x2f]\n          const code: number = c.charCodeAt(0);\n          if (code < 0x20 || code > 0x3f) {\n            result += AnsiEscape.removeCodes(state.buffer);\n            state.buffer = '';\n            state.parseState = State.Start;\n          }\n        }\n      }\n    }\n\n    return result;\n  }\n\n  public close(unknownState: TextRewriterState): string {\n    const state: IRemoveColorsTextRewriterState = unknownState as IRemoveColorsTextRewriterState;\n\n    const result: string = state.buffer;\n    state.buffer = '';\n    return result;\n  }\n}\n"]}