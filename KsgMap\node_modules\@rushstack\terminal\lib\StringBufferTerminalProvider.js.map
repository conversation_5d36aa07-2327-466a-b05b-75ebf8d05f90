{"version": 3, "file": "StringBufferTerminalProvider.js", "sourceRoot": "", "sources": ["../src/StringBufferTerminalProvider.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,oEAAmE;AACnE,2DAAuF;AACvF,6CAA0C;AAe1C;;;;;;GAMG;AACH,MAAa,4BAA4B;IASvC,YAAmB,gBAAyB,KAAK;QARzC,oBAAe,GAAkB,IAAI,iCAAa,EAAE,CAAC;QACrD,mBAAc,GAAkB,IAAI,iCAAa,EAAE,CAAC;QACpD,iBAAY,GAAkB,IAAI,iCAAa,EAAE,CAAC;QAClD,mBAAc,GAAkB,IAAI,iCAAa,EAAE,CAAC;QACpD,iBAAY,GAAkB,IAAI,iCAAa,EAAE,CAAC;QAKxD,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,IAAY,EAAE,QAAkC;QAC3D,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,4CAAwB,CAAC,OAAO,CAAC,CAAC,CAAC;gBACtC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM;YACR,CAAC;YAED,KAAK,4CAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;gBACpC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC/B,MAAM;YACR,CAAC;YAED,KAAK,4CAAwB,CAAC,OAAO,CAAC,CAAC,CAAC;gBACtC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM;YACR,CAAC;YAED,KAAK,4CAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;gBACpC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC/B,MAAM;YACR,CAAC;YAED,KAAK,4CAAwB,CAAC,GAAG,CAAC;YAClC,OAAO,CAAC,CAAC,CAAC;gBACR,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAClC,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,OAAoC;QACnD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,OAAoC;QACpD,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,OAAoC;QAC1D,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,OAAoC;QACxD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,OAAoC;QACxD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,OAAoC;QAC1D,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAEO,gBAAgB,CAAC,CAAS,EAAE,OAA+C;QACjF,OAAO,GAAG;YACR,0BAA0B,EAAE,IAAI;YAEhC,GAAG,CAAC,OAAO,IAAI,EAAE,CAAC;SACnB,CAAC;QAEF,CAAC,GAAG,wBAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAExB,IAAI,OAAO,CAAC,0BAA0B,EAAE,CAAC;YACvC,OAAO,uBAAU,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC;QAChE,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;CACF;AArHD,oEAqHC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport { StringBuilder, Text } from '@rushstack/node-core-library';\nimport { type ITerminalProvider, TerminalProviderSeverity } from './ITerminalProvider';\nimport { AnsiEscape } from './AnsiEscape';\n\n/**\n * @beta\n */\nexport interface IStringBufferOutputOptions {\n  /**\n   * If set to true, special characters like \\\\n, \\\\r, and the \\\\u001b character\n   * in color control tokens will get normalized to [-n-], [-r-], and [-x-] respectively\n   *\n   * This option defaults to `true`\n   */\n  normalizeSpecialCharacters: boolean;\n}\n\n/**\n * Terminal provider that stores written data in buffers separated by severity.\n * This terminal provider is designed to be used when code that prints to a terminal\n * is being unit tested.\n *\n * @beta\n */\nexport class StringBufferTerminalProvider implements ITerminalProvider {\n  private _standardBuffer: StringBuilder = new StringBuilder();\n  private _verboseBuffer: StringBuilder = new StringBuilder();\n  private _debugBuffer: StringBuilder = new StringBuilder();\n  private _warningBuffer: StringBuilder = new StringBuilder();\n  private _errorBuffer: StringBuilder = new StringBuilder();\n\n  private _supportsColor: boolean;\n\n  public constructor(supportsColor: boolean = false) {\n    this._supportsColor = supportsColor;\n  }\n\n  /**\n   * {@inheritDoc ITerminalProvider.write}\n   */\n  public write(data: string, severity: TerminalProviderSeverity): void {\n    switch (severity) {\n      case TerminalProviderSeverity.warning: {\n        this._warningBuffer.append(data);\n        break;\n      }\n\n      case TerminalProviderSeverity.error: {\n        this._errorBuffer.append(data);\n        break;\n      }\n\n      case TerminalProviderSeverity.verbose: {\n        this._verboseBuffer.append(data);\n        break;\n      }\n\n      case TerminalProviderSeverity.debug: {\n        this._debugBuffer.append(data);\n        break;\n      }\n\n      case TerminalProviderSeverity.log:\n      default: {\n        this._standardBuffer.append(data);\n        break;\n      }\n    }\n  }\n\n  /**\n   * {@inheritDoc ITerminalProvider.eolCharacter}\n   */\n  public get eolCharacter(): string {\n    return '\\n';\n  }\n\n  /**\n   * {@inheritDoc ITerminalProvider.supportsColor}\n   */\n  public get supportsColor(): boolean {\n    return this._supportsColor;\n  }\n\n  /**\n   * Get everything that has been written at log-level severity.\n   */\n  public getOutput(options?: IStringBufferOutputOptions): string {\n    return this._normalizeOutput(this._standardBuffer.toString(), options);\n  }\n\n  /**\n   * @deprecated - use {@link StringBufferTerminalProvider.getVerboseOutput}\n   */\n  public getVerbose(options?: IStringBufferOutputOptions): string {\n    return this.getVerboseOutput(options);\n  }\n\n  /**\n   * Get everything that has been written at verbose-level severity.\n   */\n  public getVerboseOutput(options?: IStringBufferOutputOptions): string {\n    return this._normalizeOutput(this._verboseBuffer.toString(), options);\n  }\n\n  /**\n   * Get everything that has been written at debug-level severity.\n   */\n  public getDebugOutput(options?: IStringBufferOutputOptions): string {\n    return this._normalizeOutput(this._debugBuffer.toString(), options);\n  }\n\n  /**\n   * Get everything that has been written at error-level severity.\n   */\n  public getErrorOutput(options?: IStringBufferOutputOptions): string {\n    return this._normalizeOutput(this._errorBuffer.toString(), options);\n  }\n\n  /**\n   * Get everything that has been written at warning-level severity.\n   */\n  public getWarningOutput(options?: IStringBufferOutputOptions): string {\n    return this._normalizeOutput(this._warningBuffer.toString(), options);\n  }\n\n  private _normalizeOutput(s: string, options: IStringBufferOutputOptions | undefined): string {\n    options = {\n      normalizeSpecialCharacters: true,\n\n      ...(options || {})\n    };\n\n    s = Text.convertToLf(s);\n\n    if (options.normalizeSpecialCharacters) {\n      return AnsiEscape.formatForTests(s, { encodeNewlines: true });\n    } else {\n      return s;\n    }\n  }\n}\n"]}