{"version": 3, "file": "FileWriter.js", "sourceRoot": "", "sources": ["../src/FileWriter.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAG3D,qCAAkC;AAElC,MAAM,GAAG,GAA8B,eAAM,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AA8BxE;;;GAGG;AACH,MAAa,UAAU;IAQrB,YAAoB,cAAsB,EAAE,QAAgB;QAC1D,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,IAAI,CAAC,QAAgB,EAAE,KAAwB;QAC3D,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IAClG,CAAC;IAED;;;OAGG;IACK,MAAM,CAAC,oBAAoB,CAAC,KAAmC;QACrE,KAAK,GAAG;YACN,MAAM,EAAE,KAAK;YACb,SAAS,EAAE,KAAK;YAChB,GAAG,KAAK;SACT,CAAC;QACF,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAkB,CAAC;IAC1F,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,IAAY;QACvB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;QACtF,CAAC;QAED,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG;IACI,KAAK;QACV,MAAM,EAAE,GAAuB,IAAI,CAAC,eAAe,CAAC;QACpD,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;YACjC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,aAAa;QAClB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;QAC5F,CAAC;QAED,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC7C,CAAC;CACF;AA7ED,gCA6EC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport type { FileSystemStats } from './FileSystem';\nimport { Import } from './Import';\n\nconst fsx: typeof import('fs-extra') = Import.lazy('fs-extra', require);\n\n/**\n * Available file handle opening flags.\n * @public\n */\ntype NodeFileFlags = 'r' | 'r+' | 'rs+' | 'w' | 'wx' | 'w+' | 'wx+' | 'a' | 'ax' | 'a+' | 'ax+';\n\n/**\n * Interface which represents the flags about which mode the file should be opened in.\n * @public\n */\nexport interface IFileWriterFlags {\n  /**\n   * Open file for appending.\n   */\n  append?: boolean;\n\n  /**\n   * Fails if path exists. The exclusive flag ensures that path is newly created.\n   *\n   * @remarks\n   * On POSIX-like operating systems, path is considered to exist even if it is a symlink to a\n   * non-existent file.  The exclusive flag may or may not work with network file systems.\n   *\n   * POSIX is a registered trademark of the Institute of Electrical and Electronic Engineers, Inc.\n   */\n  exclusive?: boolean;\n}\n\n/**\n * API for interacting with file handles.\n * @public\n */\nexport class FileWriter {\n  /**\n   * The `filePath` that was passed to {@link FileWriter.open}.\n   */\n  public readonly filePath: string;\n\n  private _fileDescriptor: number | undefined;\n\n  private constructor(fileDescriptor: number, filePath: string) {\n    this._fileDescriptor = fileDescriptor;\n    this.filePath = filePath;\n  }\n\n  /**\n   * Opens a new file handle to the file at the specified path and given mode.\n   * Behind the scenes it uses `fs.openSync()`.\n   * The behaviour of this function is platform specific.\n   * See: https://nodejs.org/docs/latest-v8.x/api/fs.html#fs_fs_open_path_flags_mode_callback\n   * @param filePath - The absolute or relative path to the file handle that should be opened.\n   * @param flags - The flags for opening the handle\n   */\n  public static open(filePath: string, flags?: IFileWriterFlags): FileWriter {\n    return new FileWriter(fsx.openSync(filePath, FileWriter._convertFlagsForNode(flags)), filePath);\n  }\n\n  /**\n   * Helper function to convert the file writer array to a Node.js style string (e.g. \"wx\" or \"a\").\n   * @param flags - The flags that should be converted.\n   */\n  private static _convertFlagsForNode(flags: IFileWriterFlags | undefined): NodeFileFlags {\n    flags = {\n      append: false,\n      exclusive: false,\n      ...flags\n    };\n    return [flags.append ? 'a' : 'w', flags.exclusive ? 'x' : ''].join('') as NodeFileFlags;\n  }\n\n  /**\n   * Writes some text to the given file handle. Throws if the file handle has been closed.\n   * Behind the scenes it uses `fs.writeSync()`.\n   * @param text - The text to write to the file.\n   */\n  public write(text: string): void {\n    if (!this._fileDescriptor) {\n      throw new Error(`Cannot write to file, file descriptor has already been released.`);\n    }\n\n    fsx.writeSync(this._fileDescriptor, text);\n  }\n\n  /**\n   * Closes the file handle permanently. No operations can be made on this file handle after calling this.\n   * Behind the scenes it uses `fs.closeSync()` and releases the file descriptor to be re-used.\n   *\n   * @remarks\n   * The `close()` method can be called more than once; additional calls are ignored.\n   */\n  public close(): void {\n    const fd: number | undefined = this._fileDescriptor;\n    if (fd) {\n      this._fileDescriptor = undefined;\n      fsx.closeSync(fd);\n    }\n  }\n\n  /**\n   * Gets the statistics for the given file handle. Throws if the file handle has been closed.\n   * Behind the scenes it uses `fs.statSync()`.\n   */\n  public getStatistics(): FileSystemStats {\n    if (!this._fileDescriptor) {\n      throw new Error(`Cannot get file statistics, file descriptor has already been released.`);\n    }\n\n    return fsx.fstatSync(this._fileDescriptor);\n  }\n}\n"]}