{"version": 3, "file": "Constants.js", "sourceRoot": "", "sources": ["../src/Constants.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D;;;;GAIG;AACH,IAAY,oBAKX;AALD,WAAY,oBAAoB;IAC9B;;OAEG;IACH,gEAAwC,CAAA;AAC1C,CAAC,EALW,oBAAoB,oCAApB,oBAAoB,QAK/B;AAEY,QAAA,uBAAuB,GAAkB,MAAM,CAAC,SAAS,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\n\n/**\n * String constants for command line processing.\n *\n * @public\n */\nexport enum CommandLineConstants {\n  /**\n   * The name of the built-in action that serves suggestions for tab-completion\n   */\n  TabCompletionActionName = 'tab-complete'\n}\n\nexport const SCOPING_PARAMETER_GROUP: unique symbol = Symbol('scoping');\n"]}