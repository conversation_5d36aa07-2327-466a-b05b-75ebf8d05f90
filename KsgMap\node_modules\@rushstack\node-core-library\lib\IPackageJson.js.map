{"version": 3, "file": "IPackageJson.js", "sourceRoot": "", "sources": ["../src/IPackageJson.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\n\n/**\n * This interface is part of the {@link IPackageJson} file format.  It is used for the\n * \"dependencies\", \"optionalDependencies\", and \"devDependencies\" fields.\n * @public\n */\nexport interface IPackageJsonDependencyTable {\n  /**\n   * The key is the name of a dependency.  The value is a Semantic Versioning (SemVer)\n   * range specifier.\n   */\n  [dependencyName: string]: string;\n}\n\n/**\n * This interface is part of the {@link IPackageJson} file format.  It is used for the\n * \"scripts\" field.\n * @public\n */\nexport interface IPackageJsonScriptTable {\n  /**\n   * The key is the name of the script hook.  The value is the script body which may\n   * be a file path or shell script command.\n   */\n  [scriptName: string]: string;\n}\n\n/**\n * This interface is part of the {@link IPackageJson} file format.  It is used for the\n * \"repository\" field.\n * @public\n */\nexport interface IPackageJsonRepository {\n  /**\n   * The source control type for the repository that hosts the project. This is typically \"git\".\n   */\n  type: string;\n\n  /**\n   * The URL of the repository that hosts the project.\n   */\n  url: string;\n\n  /**\n   * If the project does not exist at the root of the repository, its path is specified here.\n   */\n  directory?: string;\n}\n\n/**\n * This interface is part of the {@link IPackageJson} file format. It is used for the\n * \"peerDependenciesMeta\" field.\n * @public\n */\nexport interface IPeerDependenciesMetaTable {\n  [dependencyName: string]: {\n    optional?: boolean;\n  };\n}\n\n/**\n * This interface is part of the {@link IPackageJson} file format. It is used for the\n * \"dependenciesMeta\" field.\n * @public\n */\nexport interface IDependenciesMetaTable {\n  [dependencyName: string]: {\n    injected?: boolean;\n  };\n}\n\n/**\n * This interface is part of the {@link IPackageJson} file format. It is used for the values\n * of the \"exports\" field.\n *\n * See {@link https://nodejs.org/api/packages.html#conditional-exports | Node.js documentation on Conditional Exports} and\n * {@link https://nodejs.org/api/packages.html#community-conditions-definitions | Node.js documentation on Community Conditional Exports}.\n *\n * @public\n */\nexport interface IPackageJsonExports {\n  /**\n   * This export is like {@link IPackageJsonExports.node} in that it matches for any NodeJS environment.\n   * This export is specifically for native C++ addons.\n   */\n  'node-addons'?: string | IPackageJsonExports;\n\n  /**\n   * This export matches for any NodeJS environment.\n   */\n  node?: string | IPackageJsonExports;\n\n  /**\n   * This export matches when loaded via ESM syntax (i.e. - `import '...'` or `import('...')`).\n   * This is always mutually exclusive with {@link IPackageJsonExports.require}.\n   */\n  import?: string | IPackageJsonExports;\n\n  /**\n   * This export matches when loaded via `require()`.\n   * This is always mutually exclusive with {@link IPackageJsonExports.import}.\n   */\n  require?: string | IPackageJsonExports;\n\n  /**\n   * This export matches as a fallback when no other conditions match. Because exports are evaluated in\n   * the order that they are specified in the `package.json` file, this condition should always come last\n   * as no later exports will match if this one does.\n   */\n  default?: string | IPackageJsonExports;\n\n  /**\n   * This export matches when loaded by the typing system (i.e. - the TypeScript compiler).\n   */\n  types?: string | IPackageJsonExports;\n\n  /**\n   * Any web browser environment.\n   */\n  browser?: string | IPackageJsonExports;\n\n  /**\n   * This export matches in development-only environments.\n   * This is always mutually exclusive with {@link IPackageJsonExports.production}.\n   */\n  development?: string | IPackageJsonExports;\n\n  /**\n   * This export matches in production-only environments.\n   * This is always mutually exclusive with {@link IPackageJsonExports.development}.\n   */\n  production?: string | IPackageJsonExports;\n}\n\n/**\n * An interface for accessing common fields from a package.json file whose version field may be missing.\n *\n * @remarks\n * This interface is the same as {@link IPackageJson}, except that the `version` field is optional.\n * According to the {@link https://docs.npmjs.com/files/package.json | NPM documentation}\n * and {@link http://wiki.commonjs.org/wiki/Packages/1.0 | CommonJS Packages specification}, the `version` field\n * is normally a required field for package.json files.\n *\n * However, NodeJS relaxes this requirement for its `require()` API.  The\n * {@link https://nodejs.org/dist/latest-v10.x/docs/api/modules.html#modules_folders_as_modules\n * | \"Folders as Modules\" section} from the NodeJS documentation gives an example of a package.json file\n * that has only the `name` and `main` fields.  NodeJS does not consider the `version` field during resolution,\n * so it can be omitted.  Some libraries do this.\n *\n * Use the `INodePackageJson` interface when loading such files.  Use `IPackageJson` for package.json files\n * that are installed from an NPM registry, or are otherwise known to have a `version` field.\n *\n * @public\n */\nexport interface INodePackageJson {\n  /**\n   * The name of the package.\n   */\n  name: string;\n\n  /**\n   * A version number conforming to the Semantic Versioning (SemVer) standard.\n   */\n  version?: string;\n\n  /**\n   * Indicates whether this package is allowed to be published or not.\n   */\n  private?: boolean;\n\n  /**\n   * A brief description of the package.\n   */\n  description?: string;\n\n  /**\n   * The URL of the project's repository.\n   */\n  repository?: string | IPackageJsonRepository;\n\n  /**\n   * The URL to the project's web page.\n   */\n  homepage?: string;\n\n  /**\n   * The name of the license.\n   */\n  license?: string;\n\n  /**\n   * The path to the module file that will act as the main entry point.\n   */\n  main?: string;\n\n  /**\n   * The path to the TypeScript *.d.ts file describing the module file\n   * that will act as the main entry point.\n   */\n  types?: string;\n\n  /**\n   * Alias for `types`\n   */\n  typings?: string;\n\n  /**\n   * The path to the TSDoc metadata file.\n   * This is still being standardized: https://github.com/microsoft/tsdoc/issues/7#issuecomment-442271815\n   * @beta\n   */\n  tsdocMetadata?: string;\n\n  /**\n   * The main entry point for the package.\n   */\n  bin?: string | Record<string, string>;\n\n  /**\n   * An array of dependencies that must always be installed for this package.\n   */\n  dependencies?: IPackageJsonDependencyTable;\n\n  /**\n   * An array of optional dependencies that may be installed for this package.\n   */\n  optionalDependencies?: IPackageJsonDependencyTable;\n\n  /**\n   * An array of dependencies that must only be installed for developers who will\n   * build this package.\n   */\n  devDependencies?: IPackageJsonDependencyTable;\n\n  /**\n   * An array of dependencies that must be installed by a consumer of this package,\n   * but which will not be automatically installed by this package.\n   */\n  peerDependencies?: IPackageJsonDependencyTable;\n\n  /**\n   * An array of metadata for dependencies declared inside dependencies, optionalDependencies, and devDependencies.\n   * https://pnpm.io/package_json#dependenciesmeta\n   */\n  dependenciesMeta?: IDependenciesMetaTable;\n\n  /**\n   * An array of metadata about peer dependencies.\n   */\n  peerDependenciesMeta?: IPeerDependenciesMetaTable;\n\n  /**\n   * A table of script hooks that a package manager or build tool may invoke.\n   */\n  scripts?: IPackageJsonScriptTable;\n\n  /**\n   * A table of package version resolutions. This feature is only implemented by the Yarn package manager.\n   *\n   * @remarks\n   * See the {@link https://github.com/yarnpkg/rfcs/blob/master/implemented/0000-selective-versions-resolutions.md\n   * | 0000-selective-versions-resolutions.md RFC} for details.\n   */\n  resolutions?: Record<string, string>;\n\n  /**\n   * A table of TypeScript *.d.ts file paths that are compatible with specific TypeScript version\n   * selectors. This data take a form similar to that of the {@link INodePackageJson.exports} field,\n   * with fallbacks listed in order in the value array for example:\n   *\n   * ```JSON\n   * \"typesVersions\": {\n   *   \">=3.1\": {\n   *     \"*\": [\"./types-3.1/*\", \"./types-3.1-fallback/*\"]\n   *   },\n   *   \">=3.0\": {\n   *     \"*\": [\"./types-legacy/*\"]\n   *   }\n   * }\n   * ```\n   *\n   * or\n   *\n   * ```JSON\n   * \"typesVersions\": {\n   *   \">=3.1\": {\n   *     \"app/*\": [\"./app/types-3.1/*\"],\n   *     \"lib/*\": [\"./lib/types-3.1/*\"]\n   *   },\n   *   \">=3.0\": {\n   *     \"app/*\": [\"./app/types-legacy/*\"],\n   *     \"lib/*\": [\"./lib/types-legacy/*\"]\n   *   }\n   * }\n   * ```\n   *\n   * See the\n   * {@link https://www.typescriptlang.org/docs/handbook/declaration-files/publishing.html#version-selection-with-typesversions\n   * | TypeScript documentation} for details.\n   */\n  typesVersions?: Record<string, Record<string, [string, ...string[]]>>;\n\n  /**\n   * The \"exports\" field is used to specify the entry points for a package.\n   * See {@link https://nodejs.org/api/packages.html#exports | Node.js documentation}\n   */\n  // eslint-disable-next-line @rushstack/no-new-null\n  exports?: string | string[] | Record<string, null | string | IPackageJsonExports>;\n\n  /**\n   * The \"files\" field is an array of file globs that should be included in the package during publishing.\n   *\n   * See the {@link https://docs.npmjs.com/cli/v6/configuring-npm/package-json#files | NPM documentation}.\n   */\n  files?: string[];\n}\n\n/**\n * An interface for accessing common fields from a package.json file.\n *\n * @remarks\n * This interface describes a package.json file format whose `name` and `version` field are required.\n * In some situations, the `version` field is optional; in that case, use the {@link INodePackageJson}\n * interface instead.\n *\n * More fields may be added to this interface in the future.  For documentation about the package.json file format,\n * see the {@link http://wiki.commonjs.org/wiki/Packages/1.0 | CommonJS Packages specification}\n * and the {@link https://docs.npmjs.com/files/package.json | NPM manual page}.\n *\n * @public\n */\nexport interface IPackageJson extends INodePackageJson {\n  // Make the \"version\" field non-optional.\n  /** {@inheritDoc INodePackageJson.version} */\n  version: string;\n}\n"]}