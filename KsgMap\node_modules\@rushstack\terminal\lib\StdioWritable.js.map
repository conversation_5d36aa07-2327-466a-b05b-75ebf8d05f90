{"version": 3, "file": "StdioWritable.js", "sourceRoot": "", "sources": ["../src/StdioWritable.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;AAE3D,sDAA8B;AAC9B,qDAA0E;AAC1E,yDAAsD;AAEtD;;;;;;;;;GASG;AACH,MAAa,aAAc,SAAQ,mCAAgB;IAGvC,YAAY,CAAC,KAAqB;QAC1C,IAAI,KAAK,CAAC,IAAI,KAAK,kCAAiB,CAAC,MAAM,EAAE,CAAC;YAC5C,iBAAO,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,kCAAiB,CAAC,MAAM,EAAE,CAAC;YACnD,iBAAO,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;;AATH,sCAUC;AATe,sBAAQ,GAAkB,IAAI,aAAa,EAAE,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See L<PERSON>EN<PERSON> in the project root for license information.\n\nimport process from 'process';\nimport { type ITerminalChunk, TerminalChunkKind } from './ITerminalChunk';\nimport { TerminalWritable } from './TerminalWritable';\n\n/**\n * A {@link TerminalWritable} subclass that writes its output directly to the process `stdout` and `stderr`\n * streams.\n *\n * @remarks\n * This is the standard output target for a process.  You normally do not need to construct\n * this class; the {@link StdioWritable.\"instance\"} singleton can be used instead.\n *\n * @public\n */\nexport class StdioWritable extends TerminalWritable {\n  public static instance: StdioWritable = new StdioWritable();\n\n  protected onWriteChunk(chunk: ITerminalChunk): void {\n    if (chunk.kind === TerminalChunkKind.Stdout) {\n      process.stdout.write(chunk.text);\n    } else if (chunk.kind === TerminalChunkKind.Stderr) {\n      process.stderr.write(chunk.text);\n    }\n  }\n}\n"]}