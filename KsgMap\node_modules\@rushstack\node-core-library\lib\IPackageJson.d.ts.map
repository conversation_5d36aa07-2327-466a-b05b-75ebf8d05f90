{"version": 3, "file": "IPackageJson.d.ts", "sourceRoot": "", "sources": ["../src/IPackageJson.ts"], "names": [], "mappings": "AAGA;;;;GAIG;AACH,MAAM,WAAW,2BAA2B;IAC1C;;;OAGG;IACH,CAAC,cAAc,EAAE,MAAM,GAAG,MAAM,CAAC;CAClC;AAED;;;;GAIG;AACH,MAAM,WAAW,uBAAuB;IACtC;;;OAGG;IACH,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM,CAAC;CAC9B;AAED;;;;GAIG;AACH,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED;;;;GAIG;AACH,MAAM,WAAW,0BAA0B;IACzC,CAAC,cAAc,EAAE,MAAM,GAAG;QACxB,QAAQ,CAAC,EAAE,OAAO,CAAC;KACpB,CAAC;CACH;AAED;;;;GAIG;AACH,MAAM,WAAW,sBAAsB;IACrC,CAAC,cAAc,EAAE,MAAM,GAAG;QACxB,QAAQ,CAAC,EAAE,OAAO,CAAC;KACpB,CAAC;CACH;AAED;;;;;;;;GAQG;AACH,MAAM,WAAW,mBAAmB;IAClC;;;OAGG;IACH,aAAa,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;IAE7C;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;IAEpC;;;OAGG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;IAEtC;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;IAEvC;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;IAEvC;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;IAErC;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;IAEvC;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;IAE3C;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;CAC3C;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,sBAAsB,CAAC;IAE7C;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;;OAGG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;;;OAIG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAEtC;;OAEG;IACH,YAAY,CAAC,EAAE,2BAA2B,CAAC;IAE3C;;OAEG;IACH,oBAAoB,CAAC,EAAE,2BAA2B,CAAC;IAEnD;;;OAGG;IACH,eAAe,CAAC,EAAE,2BAA2B,CAAC;IAE9C;;;OAGG;IACH,gBAAgB,CAAC,EAAE,2BAA2B,CAAC;IAE/C;;;OAGG;IACH,gBAAgB,CAAC,EAAE,sBAAsB,CAAC;IAE1C;;OAEG;IACH,oBAAoB,CAAC,EAAE,0BAA0B,CAAC;IAElD;;OAEG;IACH,OAAO,CAAC,EAAE,uBAAuB,CAAC;IAElC;;;;;;OAMG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAErC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;IAEtE;;;OAGG;IAEH,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,GAAG,MAAM,GAAG,mBAAmB,CAAC,CAAC;IAElF;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;CAClB;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,WAAW,YAAa,SAAQ,gBAAgB;IAEpD,6CAA6C;IAC7C,OAAO,EAAE,MAAM,CAAC;CACjB"}