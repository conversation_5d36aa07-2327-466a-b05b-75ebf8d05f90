{"version": 3, "file": "Import.js", "sourceRoot": "", "sources": ["../src/Import.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,2CAA6B;AAC7B,0CAA2C;AAC3C,iDAAmC;AACnC,qCAAsC;AAEtC,2DAAwD;AACxD,6CAA0C;AAE1C,+CAA4C;AA2I5C;;;GAGG;AACH,MAAa,MAAM;IAET,MAAM,KAAK,eAAe;QAChC,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAC7B,MAAM,CAAC,gBAAgB,GAAG,IAAI,GAAG,CAAS,UAAU,CAAC,cAAc,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,MAAM,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqEG;IACH,8DAA8D;IACvD,MAAM,CAAC,IAAI,CAAC,UAAkB,EAAE,OAAgC;QACrE,MAAM,eAAe,GAAoC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC7E,OAAO,eAAe,CAAC,UAAU,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACI,MAAM,CAAC,aAAa,CAAC,OAAoC;QAC9D,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAEtG,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAChC,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,MAAM,kBAAkB,GAAW,CAAC,WAAW,IAAI,uBAAU,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,CAAC;QAE3F,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/B,+CAA+C;YAC/C,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC;QAED,sFAAsF;QACtF,iCAAiC;QACjC,MAAM,UAAU,GAAW,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnD,MAAM,UAAU,GAAW,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QAC5F,IAAI,CAAC,oBAAoB,IAAI,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,uBAAuB,UAAU,WAAW,OAAO,CAAC,cAAc,IAAI,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,kBAAkB,KAAK,IAAI,EAAE,CAAC;YAChC,MAAM,UAAU,GAAmC,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;YAC9F,IACE,UAAU;gBACV,CAAC,UAAU,KAAK,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,WAAW,GAAG,CAAC,CAAC,EAC9F,CAAC;gBACD,MAAM,WAAW,GAAW,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChF,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YACH,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE;gBAC9B,OAAO,EAAE,kBAAkB;gBAC3B,gBAAgB,EAAE,KAAK;gBACvB,YAAY,EAAE,WAAW;aAC1B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,CAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,UAAU,WAAW,OAAO,CAAC,cAAc,MAAM,CAAC,EAAE,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,OAAyC;QAC9E,MAAM,EACJ,UAAU,EACV,cAAc,EACd,oBAAoB,EACpB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,EACjB,GAAG,OAAO,CAAC;QAEZ,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAChC,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,MAAM,kBAAkB,GAAW,MAAM,CAAC,gBAAgB,IAAI,WAAW,IAAI,uBAAU,CAAC,gBAAgB,CAAC,CACvG,cAAc,CACf,CAAC;QAEF,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/B,+CAA+C;YAC/C,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC;QAED,sFAAsF;QACtF,iCAAiC;QACjC,MAAM,UAAU,GAAW,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnD,MAAM,UAAU,GAAW,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QAC5F,IAAI,CAAC,oBAAoB,IAAI,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,uBAAuB,UAAU,WAAW,OAAO,CAAC,cAAc,IAAI,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,kBAAkB,KAAK,IAAI,EAAE,CAAC;YAChC,MAAM,UAAU,GAAmC,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;YAC9F,IACE,UAAU;gBACV,CAAC,UAAU,KAAK,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,WAAW,GAAG,CAAC,CAAC,EAC9F,CAAC;gBACD,MAAM,WAAW,GAAW,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChF,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YACH,MAAM,cAAc,GAAoB,IAAI,OAAO,CACjD,CAAC,OAAuC,EAAE,MAA8B,EAAE,EAAE;gBAC1E,MAAM,UAAU,GACd,gBAAgB,IAAI,WAAW;oBAC7B,CAAC,CAAC,CAAC,QAAgB,EAAE,QAA8D,EAAE,EAAE;wBACnF,IAAI,gBAAgB,EAAE,CAAC;4BACrB,gBAAgB,CAAC,QAAQ,CAAC;iCACvB,IAAI,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;iCACpD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;wBACvC,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC;gCACH,MAAM,YAAY,GAAW,WAAY,CAAC,QAAQ,CAAC,CAAC;gCACpD,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;4BAC/B,CAAC;4BAAC,OAAO,KAAc,EAAE,CAAC;gCACxB,QAAQ,CAAC,KAAc,CAAC,CAAC;4BAC3B,CAAC;wBACH,CAAC;oBACH,CAAC;oBACH,CAAC,CAAC,SAAS,CAAC;gBAEhB,OAAO,CAAC,OAAO,CACb,UAAU,EACV;oBACE,OAAO,EAAE,kBAAkB;oBAC3B,gBAAgB,EAAE,KAAK;oBACvB,QAAQ,EAAE,UAAU;iBACrB,EACD,CAAC,KAAmB,EAAE,YAAqB,EAAE,EAAE;oBAC7C,IAAI,KAAK,EAAE,CAAC;wBACV,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC;yBAAM,CAAC;wBACN,kFAAkF;wBAClF,8DAA8D;wBAC9D,OAAO,CAAC,YAAa,CAAC,CAAC;oBACzB,CAAC;gBACH,CAAC,CACF,CAAC;YACJ,CAAC,CACF,CAAC;YACF,OAAO,MAAM,cAAc,CAAC;QAC9B,CAAC;QAAC,OAAO,CAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,UAAU,WAAW,OAAO,CAAC,cAAc,MAAM,CAAC,EAAE,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACI,MAAM,CAAC,cAAc,CAAC,OAAqC;QAChE,MAAM,EACJ,WAAW,EACX,oBAAoB,EACpB,cAAc,EACd,kBAAkB,EAClB,WAAW,EACX,iBAAiB,EAClB,GAAG,OAAO,CAAC;QAEZ,IAAI,oBAAoB,IAAI,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACpE,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,MAAM,kBAAkB,GAAW,CAAC,WAAW,IAAI,uBAAU,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,CAAC;QAE3F,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,UAAU,GAAmC,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;YAC9F,IAAI,UAAU,IAAI,UAAU,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC;gBACzD,OAAO,UAAU,CAAC,eAAe,CAAC;YACpC,CAAC;QACH,CAAC;QAED,yBAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,8DAA8D;QAE9F,IAAI,CAAC;YACH,MAAM,YAAY,GAAW,iBAAiB;gBAC5C,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,WAAW,eAAe,EAAE;oBAC7C,KAAK,EAAE,CAAC,kBAAkB,CAAC;iBAC5B,CAAC;gBACJ,CAAC,CAAC,2GAA2G;oBAC3G,kDAAkD;oBAClD,OAAO,CAAC,IAAI,CAAC,GAAG,WAAW,eAAe,EAAE;wBAC1C,OAAO,EAAE,kBAAkB;wBAC3B,gBAAgB,EAAE,KAAK;wBACvB,YAAY,EAAE,WAAW;qBAC1B,CAAC,CAAC;YAEP,MAAM,WAAW,GAAW,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACvD,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,CAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,wBAAwB,WAAW,WAAW,cAAc,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAA0C;QAChF,MAAM,EACJ,WAAW,EACX,oBAAoB,EACpB,cAAc,EACd,kBAAkB,EAClB,WAAW,EACX,gBAAgB,EACjB,GAAG,OAAO,CAAC;QAEZ,IAAI,oBAAoB,IAAI,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACpE,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,MAAM,kBAAkB,GAAW,MAAM,CAAC,gBAAgB,IAAI,WAAW,IAAI,uBAAU,CAAC,gBAAgB,CAAC,CACvG,cAAc,CACf,CAAC;QAEF,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,UAAU,GAAmC,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;YAC9F,IAAI,UAAU,IAAI,UAAU,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC;gBACzD,OAAO,UAAU,CAAC,eAAe,CAAC;YACpC,CAAC;QACH,CAAC;QAED,yBAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,8DAA8D;QAE9F,IAAI,CAAC;YACH,MAAM,cAAc,GAAoB,IAAI,OAAO,CACjD,CAAC,OAAuC,EAAE,MAA8B,EAAE,EAAE;gBAC1E,MAAM,UAAU,GACd,gBAAgB,IAAI,WAAW;oBAC7B,CAAC,CAAC,CAAC,QAAgB,EAAE,QAA8D,EAAE,EAAE;wBACnF,IAAI,gBAAgB,EAAE,CAAC;4BACrB,gBAAgB,CAAC,QAAQ,CAAC;iCACvB,IAAI,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;iCACpD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;wBACvC,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC;gCACH,MAAM,YAAY,GAAW,WAAY,CAAC,QAAQ,CAAC,CAAC;gCACpD,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;4BAC/B,CAAC;4BAAC,OAAO,KAAc,EAAE,CAAC;gCACxB,QAAQ,CAAC,KAAc,CAAC,CAAC;4BAC3B,CAAC;wBACH,CAAC;oBACH,CAAC;oBACH,CAAC,CAAC,SAAS,CAAC;gBAEhB,OAAO,CAAC,OAAO;gBACb,sGAAsG;gBACtG,kDAAkD;gBAClD,GAAG,WAAW,eAAe,EAC7B;oBACE,OAAO,EAAE,kBAAkB;oBAC3B,gBAAgB,EAAE,KAAK;oBACvB,QAAQ,EAAE,UAAU;iBACrB,EACD,CAAC,KAAmB,EAAE,YAAqB,EAAE,EAAE;oBAC7C,IAAI,KAAK,EAAE,CAAC;wBACV,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC;yBAAM,CAAC;wBACN,kFAAkF;wBAClF,8DAA8D;wBAC9D,OAAO,CAAC,YAAa,CAAC,CAAC;oBACzB,CAAC;gBACH,CAAC,CACF,CAAC;YACJ,CAAC,CACF,CAAC;YACF,MAAM,YAAY,GAAW,MAAM,cAAc,CAAC;YAElD,MAAM,WAAW,GAAW,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACvD,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,CAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,wBAAwB,WAAW,WAAW,cAAc,MAAM,CAAC,EAAE,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,eAAe,CAAC,QAAgB;QAC7C,MAAM,eAAe,GACnB,qCAAiB,CAAC,QAAQ,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC;QACpE,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,WAAW,GAAiB,qCAAiB,CAAC,QAAQ,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC9F,OAAO;gBACL,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;gBAC9C,WAAW,EAAE,WAAW,CAAC,IAAI;aAC9B,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;CACF;AApZD,wBAoZC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as path from 'path';\nimport importLazy = require('import-lazy');\nimport * as Resolve from 'resolve';\nimport nodeModule = require('module');\n\nimport { PackageJsonLookup } from './PackageJsonLookup';\nimport { FileSystem } from './FileSystem';\nimport type { IPackageJson } from './IPackageJson';\nimport { PackageName } from './PackageName';\n\ntype RealpathFnType = Parameters<typeof Resolve.default>[1]['realpath'];\n\n/**\n * Common options shared by {@link IImportResolveModuleOptions} and {@link IImportResolvePackageOptions}\n * @public\n */\nexport interface IImportResolveOptions {\n  /**\n   * The path from which {@link IImportResolveModuleOptions.modulePath} or\n   * {@link IImportResolvePackageOptions.packageName} should be resolved.\n   */\n  baseFolderPath: string;\n\n  /**\n   * If true, if the package name matches a Node.js system module, then the return\n   * value will be the package name without any path.\n   *\n   * @remarks\n   * This will take precedence over an installed NPM package of the same name.\n   *\n   * Example:\n   * ```ts\n   * // Returns the string \"fs\" indicating the Node.js system module\n   * Import.resolveModulePath({\n   *   resolvePath: \"fs\",\n   *   basePath: process.cwd()\n   * })\n   * ```\n   */\n  includeSystemModules?: boolean;\n\n  /**\n   * If true, then resolvePath is allowed to refer to the package.json of the active project.\n   *\n   * @remarks\n   * This will take precedence over any installed dependency with the same name.\n   * Note that this requires an additional PackageJsonLookup calculation.\n   *\n   * Example:\n   * ```ts\n   * // Returns an absolute path to the current package\n   * Import.resolveModulePath({\n   *   resolvePath: \"current-project\",\n   *   basePath: process.cwd(),\n   *   allowSelfReference: true\n   * })\n   * ```\n   */\n  allowSelfReference?: boolean;\n\n  /**\n   * A function used to resolve the realpath of a provided file path.\n   *\n   * @remarks\n   * This is used to resolve symlinks and other non-standard file paths. By default, this uses the\n   * {@link FileSystem.getRealPath} function. However, it can be overridden to use a custom implementation\n   * which may be faster, more accurate, or provide support for additional non-standard file paths.\n   */\n  getRealPath?: (filePath: string) => string;\n}\n\n/**\n * Common options shared by {@link IImportResolveModuleAsyncOptions} and {@link IImportResolvePackageAsyncOptions}\n * @public\n */\nexport interface IImportResolveAsyncOptions extends IImportResolveOptions {\n  /**\n   * A function used to resolve the realpath of a provided file path.\n   *\n   * @remarks\n   * This is used to resolve symlinks and other non-standard file paths. By default, this uses the\n   * {@link FileSystem.getRealPath} function. However, it can be overridden to use a custom implementation\n   * which may be faster, more accurate, or provide support for additional non-standard file paths.\n   */\n  getRealPathAsync?: (filePath: string) => Promise<string>;\n}\n\n/**\n * Options for {@link Import.resolveModule}\n * @public\n */\nexport interface IImportResolveModuleOptions extends IImportResolveOptions {\n  /**\n   * The module identifier to resolve. For example \"\\@rushstack/node-core-library\" or\n   * \"\\@rushstack/node-core-library/lib/index.js\"\n   */\n  modulePath: string;\n}\n\n/**\n * Options for {@link Import.resolveModuleAsync}\n * @public\n */\nexport interface IImportResolveModuleAsyncOptions extends IImportResolveAsyncOptions {\n  /**\n   * The module identifier to resolve. For example \"\\@rushstack/node-core-library\" or\n   * \"\\@rushstack/node-core-library/lib/index.js\"\n   */\n  modulePath: string;\n}\n\n/**\n * Options for {@link Import.resolvePackage}\n * @public\n */\nexport interface IImportResolvePackageOptions extends IImportResolveOptions {\n  /**\n   * The package name to resolve. For example \"\\@rushstack/node-core-library\"\n   */\n  packageName: string;\n\n  /**\n   * If true, then the module path will be resolved using Node.js's built-in resolution algorithm.\n   *\n   * @remarks\n   * This allows reusing Node's built-in resolver cache.\n   * This implies `allowSelfReference: true`. The passed `getRealPath` will only be used on `baseFolderPath`.\n   */\n  useNodeJSResolver?: boolean;\n}\n\n/**\n * Options for {@link Import.resolvePackageAsync}\n * @public\n */\nexport interface IImportResolvePackageAsyncOptions extends IImportResolveAsyncOptions {\n  /**\n   * The package name to resolve. For example \"\\@rushstack/node-core-library\"\n   */\n  packageName: string;\n}\n\ninterface IPackageDescriptor {\n  packageRootPath: string;\n  packageName: string;\n}\n\n/**\n * Helpers for resolving and importing Node.js modules.\n * @public\n */\nexport class Import {\n  private static __builtInModules: Set<string> | undefined;\n  private static get _builtInModules(): Set<string> {\n    if (!Import.__builtInModules) {\n      Import.__builtInModules = new Set<string>(nodeModule.builtinModules);\n    }\n\n    return Import.__builtInModules;\n  }\n\n  /**\n   * Provides a way to improve process startup times by lazy-loading imported modules.\n   *\n   * @remarks\n   * This is a more structured wrapper for the {@link https://www.npmjs.com/package/import-lazy|import-lazy}\n   * package.  It enables you to replace an import like this:\n   *\n   * ```ts\n   * import * as example from 'example'; // <-- 100ms load time\n   *\n   * if (condition) {\n   *   example.doSomething();\n   * }\n   * ```\n   *\n   * ...with a pattern like this:\n   *\n   * ```ts\n   * const example: typeof import('example') = Import.lazy('example', require);\n   *\n   * if (condition) {\n   *   example.doSomething(); // <-- 100ms load time occurs here, only if needed\n   * }\n   * ```\n   *\n   * The implementation relies on JavaScript's `Proxy` feature to intercept access to object members.  Thus\n   * it will only work correctly with certain types of module exports.  If a particular export isn't well behaved,\n   * you may need to find (or introduce) some other module in your dependency graph to apply the optimization to.\n   *\n   * Usage guidelines:\n   *\n   * - Always specify types using `typeof` as shown above.\n   *\n   * - Never apply lazy-loading in a way that would convert the module's type to `any`. Losing type safety\n   *   seriously impacts the maintainability of the code base.\n   *\n   * - In cases where the non-runtime types are needed, import them separately using the `Types` suffix:\n   *\n   * ```ts\n   * const example: typeof import('example') = Import.lazy('example', require);\n   * import type * as exampleTypes from 'example';\n   * ```\n   *\n   * - If the imported module confusingly has the same name as its export, then use the Module suffix:\n   *\n   * ```ts\n   * const exampleModule: typeof import('../../logic/Example') = Import.lazy(\n   *   '../../logic/Example', require);\n   * import type * as exampleTypes from '../../logic/Example';\n   * ```\n   *\n   * - If the exports cause a lot of awkwardness (e.g. too many expressions need to have `exampleModule.` inserted\n   *   into them), or if some exports cannot be proxied (e.g. `Import.lazy('example', require)` returns a function\n   *   signature), then do not lazy-load that module.  Instead, apply lazy-loading to some other module which is\n   *   better behaved.\n   *\n   * - It's recommended to sort imports in a standard ordering:\n   *\n   * ```ts\n   * // 1. external imports\n   * import * as path from 'path';\n   * import { Import, JsonFile, JsonObject } from '@rushstack/node-core-library';\n   *\n   * // 2. local imports\n   * import { LocalFile } from './path/LocalFile';\n   *\n   * // 3. lazy-imports (which are technically variables, not imports)\n   * const semver: typeof import('semver') = Import.lazy('semver', require);\n   * ```\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public static lazy(moduleName: string, require: (id: string) => unknown): any {\n    const importLazyLocal: (moduleName: string) => unknown = importLazy(require);\n    return importLazyLocal(moduleName);\n  }\n\n  /**\n   * This resolves a module path using similar logic as the Node.js `require.resolve()` API,\n   * but supporting extra features such as specifying the base folder.\n   *\n   * @remarks\n   * A module path is a text string that might appear in a statement such as\n   * `import { X } from \"____\";` or `const x = require(\"___\");`.  The implementation is based\n   * on the popular `resolve` NPM package.\n   *\n   * Suppose `example` is an NPM package whose entry point is `lib/index.js`:\n   * ```ts\n   * // Returns \"/path/to/project/node_modules/example/lib/index.js\"\n   * Import.resolveModule({ modulePath: 'example' });\n   *\n   * // Returns \"/path/to/project/node_modules/example/lib/other.js\"\n   * Import.resolveModule({ modulePath: 'example/lib/other' });\n   * ```\n   * If you need to determine the containing package folder\n   * (`/path/to/project/node_modules/example`), use {@link Import.resolvePackage} instead.\n   *\n   * @returns the absolute path of the resolved module.\n   * If {@link IImportResolveOptions.includeSystemModules} is specified\n   * and a system module is found, then its name is returned without any file path.\n   */\n  public static resolveModule(options: IImportResolveModuleOptions): string {\n    const { modulePath, baseFolderPath, includeSystemModules, allowSelfReference, getRealPath } = options;\n\n    if (path.isAbsolute(modulePath)) {\n      return modulePath;\n    }\n\n    const normalizedRootPath: string = (getRealPath || FileSystem.getRealPath)(baseFolderPath);\n\n    if (modulePath.startsWith('.')) {\n      // This looks like a conventional relative path\n      return path.resolve(normalizedRootPath, modulePath);\n    }\n\n    // Built-in modules do not have a scope, so if there is a slash, then we need to check\n    // against the first path segment\n    const slashIndex: number = modulePath.indexOf('/');\n    const moduleName: string = slashIndex === -1 ? modulePath : modulePath.slice(0, slashIndex);\n    if (!includeSystemModules && Import._builtInModules.has(moduleName)) {\n      throw new Error(`Cannot find module \"${modulePath}\" from \"${options.baseFolderPath}\".`);\n    }\n\n    if (allowSelfReference === true) {\n      const ownPackage: IPackageDescriptor | undefined = Import._getPackageName(normalizedRootPath);\n      if (\n        ownPackage &&\n        (modulePath === ownPackage.packageName || modulePath.startsWith(`${ownPackage.packageName}/`))\n      ) {\n        const packagePath: string = modulePath.slice(ownPackage.packageName.length + 1);\n        return path.resolve(ownPackage.packageRootPath, packagePath);\n      }\n    }\n\n    try {\n      return Resolve.sync(modulePath, {\n        basedir: normalizedRootPath,\n        preserveSymlinks: false,\n        realpathSync: getRealPath\n      });\n    } catch (e: unknown) {\n      throw new Error(`Cannot find module \"${modulePath}\" from \"${options.baseFolderPath}\": ${e}`);\n    }\n  }\n\n  /**\n   * Async version of {@link Import.resolveModule}.\n   */\n  public static async resolveModuleAsync(options: IImportResolveModuleAsyncOptions): Promise<string> {\n    const {\n      modulePath,\n      baseFolderPath,\n      includeSystemModules,\n      allowSelfReference,\n      getRealPath,\n      getRealPathAsync\n    } = options;\n\n    if (path.isAbsolute(modulePath)) {\n      return modulePath;\n    }\n\n    const normalizedRootPath: string = await (getRealPathAsync || getRealPath || FileSystem.getRealPathAsync)(\n      baseFolderPath\n    );\n\n    if (modulePath.startsWith('.')) {\n      // This looks like a conventional relative path\n      return path.resolve(normalizedRootPath, modulePath);\n    }\n\n    // Built-in modules do not have a scope, so if there is a slash, then we need to check\n    // against the first path segment\n    const slashIndex: number = modulePath.indexOf('/');\n    const moduleName: string = slashIndex === -1 ? modulePath : modulePath.slice(0, slashIndex);\n    if (!includeSystemModules && Import._builtInModules.has(moduleName)) {\n      throw new Error(`Cannot find module \"${modulePath}\" from \"${options.baseFolderPath}\".`);\n    }\n\n    if (allowSelfReference === true) {\n      const ownPackage: IPackageDescriptor | undefined = Import._getPackageName(normalizedRootPath);\n      if (\n        ownPackage &&\n        (modulePath === ownPackage.packageName || modulePath.startsWith(`${ownPackage.packageName}/`))\n      ) {\n        const packagePath: string = modulePath.slice(ownPackage.packageName.length + 1);\n        return path.resolve(ownPackage.packageRootPath, packagePath);\n      }\n    }\n\n    try {\n      const resolvePromise: Promise<string> = new Promise(\n        (resolve: (resolvedPath: string) => void, reject: (error: Error) => void) => {\n          const realPathFn: RealpathFnType =\n            getRealPathAsync || getRealPath\n              ? (filePath: string, callback: (error: Error | null, resolvedPath?: string) => void) => {\n                  if (getRealPathAsync) {\n                    getRealPathAsync(filePath)\n                      .then((resolvedPath) => callback(null, resolvedPath))\n                      .catch((error) => callback(error));\n                  } else {\n                    try {\n                      const resolvedPath: string = getRealPath!(filePath);\n                      callback(null, resolvedPath);\n                    } catch (error: unknown) {\n                      callback(error as Error);\n                    }\n                  }\n                }\n              : undefined;\n\n          Resolve.default(\n            modulePath,\n            {\n              basedir: normalizedRootPath,\n              preserveSymlinks: false,\n              realpath: realPathFn\n            },\n            (error: Error | null, resolvedPath?: string) => {\n              if (error) {\n                reject(error);\n              } else {\n                // Resolve docs state that either an error will be returned, or the resolved path.\n                // In this case, the resolved path should always be populated.\n                resolve(resolvedPath!);\n              }\n            }\n          );\n        }\n      );\n      return await resolvePromise;\n    } catch (e: unknown) {\n      throw new Error(`Cannot find module \"${modulePath}\" from \"${options.baseFolderPath}\": ${e}`);\n    }\n  }\n\n  /**\n   * Performs module resolution to determine the folder where a package is installed.\n   *\n   * @remarks\n   * Suppose `example` is an NPM package whose entry point is `lib/index.js`:\n   * ```ts\n   * // Returns \"/path/to/project/node_modules/example\"\n   * Import.resolvePackage({ packageName: 'example' });\n   * ```\n   *\n   * If you need to resolve a module path, use {@link Import.resolveModule} instead:\n   * ```ts\n   * // Returns \"/path/to/project/node_modules/example/lib/index.js\"\n   * Import.resolveModule({ modulePath: 'example' });\n   * ```\n   *\n   * @returns the absolute path of the package folder.\n   * If {@link IImportResolveOptions.includeSystemModules} is specified\n   * and a system module is found, then its name is returned without any file path.\n   */\n  public static resolvePackage(options: IImportResolvePackageOptions): string {\n    const {\n      packageName,\n      includeSystemModules,\n      baseFolderPath,\n      allowSelfReference,\n      getRealPath,\n      useNodeJSResolver\n    } = options;\n\n    if (includeSystemModules && Import._builtInModules.has(packageName)) {\n      return packageName;\n    }\n\n    const normalizedRootPath: string = (getRealPath || FileSystem.getRealPath)(baseFolderPath);\n\n    if (allowSelfReference) {\n      const ownPackage: IPackageDescriptor | undefined = Import._getPackageName(normalizedRootPath);\n      if (ownPackage && ownPackage.packageName === packageName) {\n        return ownPackage.packageRootPath;\n      }\n    }\n\n    PackageName.parse(packageName); // Ensure the package name is valid and doesn't contain a path\n\n    try {\n      const resolvedPath: string = useNodeJSResolver\n        ? require.resolve(`${packageName}/package.json`, {\n            paths: [normalizedRootPath]\n          })\n        : // Append `/package.json` to ensure `resolve.sync` doesn't attempt to return a system package, and to avoid\n          // having to mess with the `packageFilter` option.\n          Resolve.sync(`${packageName}/package.json`, {\n            basedir: normalizedRootPath,\n            preserveSymlinks: false,\n            realpathSync: getRealPath\n          });\n\n      const packagePath: string = path.dirname(resolvedPath);\n      return packagePath;\n    } catch (e: unknown) {\n      throw new Error(`Cannot find package \"${packageName}\" from \"${baseFolderPath}\": ${e}.`);\n    }\n  }\n\n  /**\n   * Async version of {@link Import.resolvePackage}.\n   */\n  public static async resolvePackageAsync(options: IImportResolvePackageAsyncOptions): Promise<string> {\n    const {\n      packageName,\n      includeSystemModules,\n      baseFolderPath,\n      allowSelfReference,\n      getRealPath,\n      getRealPathAsync\n    } = options;\n\n    if (includeSystemModules && Import._builtInModules.has(packageName)) {\n      return packageName;\n    }\n\n    const normalizedRootPath: string = await (getRealPathAsync || getRealPath || FileSystem.getRealPathAsync)(\n      baseFolderPath\n    );\n\n    if (allowSelfReference) {\n      const ownPackage: IPackageDescriptor | undefined = Import._getPackageName(normalizedRootPath);\n      if (ownPackage && ownPackage.packageName === packageName) {\n        return ownPackage.packageRootPath;\n      }\n    }\n\n    PackageName.parse(packageName); // Ensure the package name is valid and doesn't contain a path\n\n    try {\n      const resolvePromise: Promise<string> = new Promise(\n        (resolve: (resolvedPath: string) => void, reject: (error: Error) => void) => {\n          const realPathFn: RealpathFnType =\n            getRealPathAsync || getRealPath\n              ? (filePath: string, callback: (error: Error | null, resolvedPath?: string) => void) => {\n                  if (getRealPathAsync) {\n                    getRealPathAsync(filePath)\n                      .then((resolvedPath) => callback(null, resolvedPath))\n                      .catch((error) => callback(error));\n                  } else {\n                    try {\n                      const resolvedPath: string = getRealPath!(filePath);\n                      callback(null, resolvedPath);\n                    } catch (error: unknown) {\n                      callback(error as Error);\n                    }\n                  }\n                }\n              : undefined;\n\n          Resolve.default(\n            // Append `/package.json` to ensure `resolve` doesn't attempt to return a system package, and to avoid\n            // having to mess with the `packageFilter` option.\n            `${packageName}/package.json`,\n            {\n              basedir: normalizedRootPath,\n              preserveSymlinks: false,\n              realpath: realPathFn\n            },\n            (error: Error | null, resolvedPath?: string) => {\n              if (error) {\n                reject(error);\n              } else {\n                // Resolve docs state that either an error will be returned, or the resolved path.\n                // In this case, the resolved path should always be populated.\n                resolve(resolvedPath!);\n              }\n            }\n          );\n        }\n      );\n      const resolvedPath: string = await resolvePromise;\n\n      const packagePath: string = path.dirname(resolvedPath);\n      return packagePath;\n    } catch (e: unknown) {\n      throw new Error(`Cannot find package \"${packageName}\" from \"${baseFolderPath}\": ${e}`);\n    }\n  }\n\n  private static _getPackageName(rootPath: string): IPackageDescriptor | undefined {\n    const packageJsonPath: string | undefined =\n      PackageJsonLookup.instance.tryGetPackageJsonFilePathFor(rootPath);\n    if (packageJsonPath) {\n      const packageJson: IPackageJson = PackageJsonLookup.instance.loadPackageJson(packageJsonPath);\n      return {\n        packageRootPath: path.dirname(packageJsonPath),\n        packageName: packageJson.name\n      };\n    } else {\n      return undefined;\n    }\n  }\n}\n"]}