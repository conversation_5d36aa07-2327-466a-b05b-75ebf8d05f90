{"version": 3, "file": "InternalError.js", "sourceRoot": "", "sources": ["../src/InternalError.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D;;;;;;;;GAQG;AACH,MAAa,aAAc,SAAQ,KAAK;IAgBtC;;;;;;OAMG;IACH,YAAmB,OAAe;QAChC,KAAK,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;QAE7C,uGAAuG;QACvG,6IAA6I;QAC7I,EAAE;QACF,4EAA4E;QAC3E,IAAY,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,yDAAyD;QAE5G,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;QAElC,IAAI,aAAa,CAAC,eAAe,EAAE,CAAC;YAClC,uCAAuC;YACvC,QAAQ,CAAC;QACX,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,kBAA0B;QACtD,OAAO,CACL,mBAAmB,kBAAkB,6DAA6D;YAClG,8DAA8D,CAC/D,CAAC;IACJ,CAAC;IAED,gBAAgB;IACT,QAAQ;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,mCAAmC;IAC1D,CAAC;;AAlDH,sCAmDC;AAlDC;;;;;;;GAOG;AACW,6BAAe,GAAY,IAAI,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\n\n/**\n * An `Error` subclass that should be thrown to report an unexpected state that may indicate a software defect.\n * An application may handle this error by instructing the end user to report an issue to the application maintainers.\n *\n * @remarks\n * Do not use this class unless you intend to solicit bug reports from end users.\n *\n * @public\n */\nexport class InternalError extends Error {\n  /**\n   * If true, a JavScript `debugger;` statement will be invoked whenever the `InternalError` constructor is called.\n   *\n   * @remarks\n   * Generally applications should not be catching and ignoring an `InternalError`.  Instead, the error should\n   * be reported and typically the application will terminate.  Thus, if `InternalError` is constructed, it's\n   * almost always something we want to examine in a debugger.\n   */\n  public static breakInDebugger: boolean = true;\n\n  /**\n   * The underlying error message, without the additional boilerplate for an `InternalError`.\n   */\n  public readonly unformattedMessage: string;\n\n  /**\n   * Constructs a new instance of the {@link InternalError} class.\n   *\n   * @param message - A message describing the error.  This will be assigned to\n   * {@link InternalError.unformattedMessage}.  The `Error.message` field will have additional boilerplate\n   * explaining that the user has encountered a software defect.\n   */\n  public constructor(message: string) {\n    super(InternalError._formatMessage(message));\n\n    // Manually set the prototype, as we can no longer extend built-in classes like Error, Array, Map, etc.\n    // https://github.com/microsoft/TypeScript-wiki/blob/main/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    //\n    // Note: the prototype must also be set on any classes which extend this one\n    (this as any).__proto__ = InternalError.prototype; // eslint-disable-line @typescript-eslint/no-explicit-any\n\n    this.unformattedMessage = message;\n\n    if (InternalError.breakInDebugger) {\n      // eslint-disable-next-line no-debugger\n      debugger;\n    }\n  }\n\n  private static _formatMessage(unformattedMessage: string): string {\n    return (\n      `Internal Error: ${unformattedMessage}\\n\\nYou have encountered a software defect. Please consider` +\n      ` reporting the issue to the maintainers of this application.`\n    );\n  }\n\n  /** @override */\n  public toString(): string {\n    return this.message; // Avoid adding the \"Error:\" prefix\n  }\n}\n"]}