{"version": 3, "file": "FileSystem.d.ts", "sourceRoot": "", "sources": ["../src/FileSystem.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAIzB,OAAO,EAAQ,KAAK,WAAW,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAC1D,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD;;;;;;GAMG;AACH,MAAM,MAAM,eAAe,GAAG,EAAE,CAAC,KAAK,CAAC;AAEvC;;;;;;GAMG;AACH,MAAM,MAAM,UAAU,GAAG,EAAE,CAAC,MAAM,CAAC;AAKnC;;;GAGG;AACH,MAAM,WAAW,4BAA4B;IAC3C;;;OAGG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,iCAAiC;IAChD;;;OAGG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAC9B;AAED;;;GAGG;AACH,MAAM,WAAW,2BAA4B,SAAQ,iCAAiC;IACpF;;;OAGG;IACH,kBAAkB,CAAC,EAAE,WAAW,CAAC;IAEjC;;;OAGG;IACH,QAAQ,CAAC,EAAE,QAAQ,CAAC;CACrB;AAED;;;GAGG;AACH,MAAM,WAAW,0BAA0B;IACzC;;;OAGG;IACH,QAAQ,CAAC,EAAE,QAAQ,CAAC;IAEpB;;;OAGG;IACH,kBAAkB,CAAC,EAAE,WAAW,CAAC;CAClC;AAED;;;GAGG;AACH,MAAM,WAAW,sBAAsB;IACrC;;;OAGG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;OAGG;IACH,eAAe,EAAE,MAAM,CAAC;IAExB;;;OAGG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IAEpB;;;OAGG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAC9B;AAED;;GAEG;AACH,MAAM,WAAW,8BAA8B;IAC7C;;;OAGG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;OAGG;IACH,qBAAqB,CAAC,EAAE,qBAAqB,CAAC;CAC/C;AAED;;;GAGG;AACH,MAAM,WAAW,0BAA2B,SAAQ,8BAA8B;IAChF;;;OAGG;IACH,eAAe,EAAE,MAAM,CAAC;CACzB;AAED;;;;;;;;;;;;GAYG;AACH,oBAAY,qBAAqB;IAC/B;;;;;;;;;;OAUG;IACH,SAAS,cAAc;IAEvB;;;OAGG;IACH,KAAK,UAAU;IAEf;;OAEG;IACH,MAAM,WAAW;CAClB;AAED;;;GAGG;AACH,MAAM,MAAM,8BAA8B,GAAG,CAC3C,UAAU,EAAE,MAAM,EAClB,eAAe,EAAE,MAAM,KACpB,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtB;;;GAGG;AACH,MAAM,MAAM,yBAAyB,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,KAAK,OAAO,CAAC;AAEjG;;;GAGG;AACH,MAAM,WAAW,gCAAgC;IAC/C;;;OAGG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;OAGG;IACH,eAAe,EAAE,MAAM,CAAC;IAExB;;OAEG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAE9B;;;;;;;OAOG;IACH,qBAAqB,CAAC,EAAE,qBAAqB,CAAC;IAE9C;;;OAGG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAE7B;;;OAGG;IACH,MAAM,CAAC,EAAE,8BAA8B,GAAG,yBAAyB,CAAC;CACrE;AAED;;;GAGG;AACH,MAAM,WAAW,2BAA4B,SAAQ,gCAAgC;IACnF,6DAA6D;IAC7D,MAAM,CAAC,EAAE,yBAAyB,CAAC;CACpC;AAED;;;GAGG;AACH,MAAM,WAAW,4BAA4B;IAC3C;;;OAGG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;CAC5B;AAED;;;;GAIG;AACH,MAAM,WAAW,+BAA+B;IAC9C;;OAEG;IACH,YAAY,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5B;;OAEG;IACH,YAAY,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B;AAED;;;;;GAKG;AACH,MAAM,WAAW,4BAA4B;IAC3C;;OAEG;IACH,cAAc,EAAE,MAAM,CAAC;IAEvB;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;;OAGG;IACH,qBAAqB,CAAC,EAAE,qBAAqB,CAAC;CAC/C;AA6CD;;;;;;;;;;;;;;;GAeG;AACH,qBAAa,UAAU;IAKrB;;;;;;;;;;OAUG;WACW,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAM3C;;OAEG;WACiB,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAQ/D;;;;;OAKG;WACW,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,eAAe;IAM1D;;OAEG;WACiB,kBAAkB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,eAAe,CAAC;IAM9E;;;;;;OAMG;WACW,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,+BAA+B,GAAG,IAAI;IAMrF;;OAEG;WACiB,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,+BAA+B,GAAG,OAAO,CAAC,IAAI,CAAC;IAQzG;;;;;OAKG;WACW,mBAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,GAAG,IAAI;IAM9E;;OAEG;WACiB,wBAAwB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IAM9F;;;;;;;;;OASG;WACW,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,aAAa;IAM3D;;OAEG;WACiB,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC;IAM/E;;;;;;OAMG;WACW,mBAAmB,CAAC,QAAQ,EAAE,aAAa,GAAG,MAAM;IAkBlE;;;OAGG;WACW,IAAI,CAAC,OAAO,EAAE,sBAAsB,GAAG,IAAI;IAyBzD;;OAEG;WACiB,SAAS,CAAC,OAAO,EAAE,sBAAsB,GAAG,OAAO,CAAC,IAAI,CAAC;IA6B7E;;;;;;OAMG;WACW,YAAY,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI;IAMpD;;OAEG;WACiB,iBAAiB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAMxE;;;;;OAKG;WACW,mBAAmB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,4BAA4B,GAAG,MAAM,EAAE;IAgBvG;;OAEG;WACiB,wBAAwB,CAC1C,UAAU,EAAE,MAAM,EAClB,OAAO,CAAC,EAAE,4BAA4B,GACrC,OAAO,CAAC,MAAM,EAAE,CAAC;IAgBpB;;;;;;OAMG;WACW,eAAe,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,4BAA4B,GAAG,UAAU,EAAE;IAmBvG;;OAEG;WACiB,oBAAoB,CACtC,UAAU,EAAE,MAAM,EAClB,OAAO,CAAC,EAAE,4BAA4B,GACrC,OAAO,CAAC,UAAU,EAAE,CAAC;IAmBxB;;;;;;OAMG;WACW,YAAY,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI;IAMpD;;OAEG;WACiB,iBAAiB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAMxE;;;;;;;OAOG;WACW,iBAAiB,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI;IAMzD;;OAEG;WACiB,sBAAsB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAU7E;;;;;;;;OAQG;WACW,SAAS,CACrB,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,GAAG,MAAM,EACzB,OAAO,CAAC,EAAE,2BAA2B,GACpC,IAAI;IA6BP;;;;;;;;;;;;OAYG;WACW,kBAAkB,CAC9B,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,aAAa,CAAC,MAAM,CAAC,eAAe,CAAC,EAC/C,OAAO,CAAC,EAAE,iCAAiC,GAC1C,IAAI;IAoDP;;OAEG;WACiB,cAAc,CAChC,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,GAAG,MAAM,EACzB,OAAO,CAAC,EAAE,2BAA2B,GACpC,OAAO,CAAC,IAAI,CAAC;IA6BhB;;OAEG;WACiB,uBAAuB,CACzC,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,aAAa,CAAC,MAAM,CAAC,eAAe,CAAC,EAC/C,OAAO,CAAC,EAAE,iCAAiC,GAC1C,OAAO,CAAC,IAAI,CAAC;IAoDhB;;;;;;;;OAQG;WACW,YAAY,CACxB,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,GAAG,MAAM,EACzB,OAAO,CAAC,EAAE,2BAA2B,GACpC,IAAI;IA6BP;;OAEG;WACiB,iBAAiB,CACnC,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,GAAG,MAAM,EACzB,OAAO,CAAC,EAAE,2BAA2B,GACpC,OAAO,CAAC,IAAI,CAAC;IA6BhB;;;;;OAKG;WACW,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,0BAA0B,GAAG,MAAM;IAgBtF;;OAEG;WACiB,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,0BAA0B,GAAG,OAAO,CAAC,MAAM,CAAC;IAgB1G;;;;OAIG;WACW,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM;IAMxD;;OAEG;WACiB,qBAAqB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAM5E;;;;;;;;;OASG;WACW,QAAQ,CAAC,OAAO,EAAE,0BAA0B,GAAG,IAAI;IAoBjE;;OAEG;WACiB,aAAa,CAAC,OAAO,EAAE,0BAA0B,GAAG,OAAO,CAAC,IAAI,CAAC;IAoBrF;;;;;;;;;OASG;WACW,SAAS,CAAC,OAAO,EAAE,2BAA2B,GAAG,IAAI;IAiBnE;;OAEG;WACiB,cAAc,CAAC,OAAO,EAAE,gCAAgC,GAAG,OAAO,CAAC,IAAI,CAAC;IAiB5F;;;;;OAKG;WACW,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,4BAA4B,GAAG,IAAI;IAiBxF;;OAEG;WACiB,eAAe,CACjC,QAAQ,EAAE,MAAM,EAChB,OAAO,CAAC,EAAE,4BAA4B,GACrC,OAAO,CAAC,IAAI,CAAC;IAqBhB;;;;OAIG;WACW,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,eAAe;IAM9D;;OAEG;WACiB,sBAAsB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,eAAe,CAAC;IAMlF;;;;;;;;;;OAUG;WACW,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAM5C;;OAEG;WACiB,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAMhE;;;;;;;;;;;;;;;;OAgBG;WACW,0BAA0B,CAAC,OAAO,EAAE,4BAA4B,GAAG,IAAI;IASrF;;OAEG;WACiB,+BAA+B,CAAC,OAAO,EAAE,4BAA4B,GAAG,OAAO,CAAC,IAAI,CAAC;IASzG;;;;;;;;;;;;OAYG;WACW,sBAAsB,CAAC,OAAO,EAAE,4BAA4B,GAAG,IAAI;IAQjF;;OAEG;WACiB,2BAA2B,CAAC,OAAO,EAAE,4BAA4B,GAAG,OAAO,CAAC,IAAI,CAAC;IAQrG;;;;;;;;;;;;OAYG;WACW,wBAAwB,CAAC,OAAO,EAAE,4BAA4B,GAAG,IAAI;IAQnF;;OAEG;WACiB,6BAA6B,CAAC,OAAO,EAAE,4BAA4B,GAAG,OAAO,CAAC,IAAI,CAAC;IAQvG;;;;;;;;;;;;;;;OAeG;WACW,cAAc,CAAC,OAAO,EAAE,4BAA4B,GAAG,IAAI;IAWzE;;OAEG;WACiB,mBAAmB,CAAC,OAAO,EAAE,4BAA4B,GAAG,OAAO,CAAC,IAAI,CAAC;IAW7F;;;;OAIG;WACW,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM;IAMnD;;OAEG;WACiB,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAUvE;;OAEG;WACW,YAAY,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAIjD;;OAEG;WACW,eAAe,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAIpD;;OAEG;WACW,uBAAuB,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAI5D;;OAEG;WACW,yBAAyB,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAI9D;;OAEG;WACW,gBAAgB,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAIrD;;OAEG;WACW,mBAAmB,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAIxD;;;OAGG;WACW,yBAAyB,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAI9D;;OAEG;WACW,gBAAgB,CAAC,KAAK,EAAE,KAAK,GAAG,KAAK,IAAI,MAAM,CAAC,cAAc;IAW5E,OAAO,CAAC,MAAM,CAAC,WAAW;mBAqCL,gBAAgB;IAwCrC,OAAO,CAAC,MAAM,CAAC,cAAc;mBASR,mBAAmB;IASxC,OAAO,CAAC,MAAM,CAAC,mBAAmB;CAoBnC"}