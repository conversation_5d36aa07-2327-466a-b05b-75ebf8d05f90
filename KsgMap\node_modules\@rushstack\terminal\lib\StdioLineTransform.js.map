{"version": 3, "file": "StdioLineTransform.js", "sourceRoot": "", "sources": ["../src/StdioLineTransform.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,oEAAiE;AAEjE,qDAA0E;AAC1E,2DAAwF;AAaxF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgDG;AACH,MAAa,mBAAoB,SAAQ,qCAAiB;IAMxD,YAAmB,OAAmC;QACpD,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAEhC,IAAI,CAAC,OAAO,GAAG,wBAAI,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,IAAI,+BAAW,CAAC,EAAE,CAAC,CAAC;IACxE,CAAC;IAES,YAAY,CAAC,KAAqB;QAC1C,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;QACjF,CAAC;QAED,qFAAqF;QACrF,sBAAsB;QACtB,MAAM,IAAI,GAAW,KAAK,CAAC,IAAI,CAAC;QAChC,IAAI,UAAU,GAAW,CAAC,CAAC;QAE3B,OAAO,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,KAAK,CAAC,IAAI,KAAK,kCAAiB,CAAC,MAAM,EAAE,CAAC;gBAC5C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YACjC,CAAC;YAED,MAAM,QAAQ,GAAW,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YACxD,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACjB,uCAAuC;gBACvC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;gBACpD,MAAM;YACR,CAAC;YAED,6BAA6B;YAC7B,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAE9D,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAE/B,cAAc;YACd,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAES,OAAO;QACf,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAEO,uBAAuB;QAC7B,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC;QAEtC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;gBAC1B,IAAI,EAAE,kCAAiB,CAAC,MAAM;gBAC9B,IAAI,EAAE,IAAI,CAAC,gBAAgB;aAC5B,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;gBAC1B,IAAI,EAAE,kCAAiB,CAAC,MAAM;gBAC9B,IAAI,EAAE,IAAI,CAAC,gBAAgB;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAClC,CAAC;CACF;AAxED,kDAwEC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See L<PERSON>EN<PERSON> in the project root for license information.\n\nimport { Text, NewlineKind } from '@rushstack/node-core-library';\n\nimport { type ITerminalChunk, TerminalChunkKind } from './ITerminalChunk';\nimport { TerminalTransform, type ITerminalTransformOptions } from './TerminalTransform';\n\n/**\n * Constructor options for {@link StderrLineTransform}\n * @beta\n */\nexport interface IStdioLineTransformOptions extends ITerminalTransformOptions {\n  /**\n   * Specifies the kind of newline for the output.\n   */\n  newlineKind?: NewlineKind;\n}\n\n/**\n * `StderrLineTransform` normalizes lines that mix characters from `stdout` and `stderr`,\n * so that each output line is routed entirely to `stdout` or `stderr`.\n *\n * @remarks\n * IMPORTANT: This transform assumes that its input has been normalized to use `\"\\n\"` newlines.\n *\n * IMPORTANT: This transform does not produce realtime output, because lines are buffered\n * until a newline character is encountered.\n *\n * Suppose that a poorly behaved process produces output like this:\n *\n * ```ts\n * process.stderr.write('An error occurred, cleaning up');\n * process.stdout.write('.'); // (delay)\n * process.stdout.write('.'); // (delay)\n * process.stdout.write('.');\n * process.stdout.write('\\n');\n * process.stderr.write('The process completed with errors\\n');\n * ```\n *\n * When `stdout` and `stderr` are combined on the console, the mistake in the output would not be noticeable:\n * ```\n * An error occurred, cleaning up...\n * The process completed with errors\n * ```\n *\n * However, if we discard `stdout`, then `stderr` is malformed:\n * ```\n * An error occurred, cleaning upThe process completed with errors\n * ```\n *\n * Tooling scripts can introduce these sorts of problems via edge cases that are difficult to find and fix.\n *\n * `StderrLineTransform` normalizes the output so that if a combined line contains any `stderr` characters,\n * then the entire line is routed to `stderr`.  Later, if we discard `stdout`, then the output will\n * preserve the appropriate context:\n *\n * ```\n * An error occurred, cleaning up...\n * The process completed with errors\n * ```\n *\n * @privateRemarks\n * This class is experimental and marked as `@beta`.  The algorithm may need some fine-tuning, or there may\n * be better solutions to this problem.\n *\n * @beta\n */\nexport class StderrLineTransform extends TerminalTransform {\n  private _accumulatedLine: string;\n  private _accumulatedStderr: boolean;\n\n  public readonly newline: string;\n\n  public constructor(options: IStdioLineTransformOptions) {\n    super(options);\n\n    this._accumulatedLine = '';\n    this._accumulatedStderr = false;\n\n    this.newline = Text.getNewline(options.newlineKind || NewlineKind.Lf);\n  }\n\n  protected onWriteChunk(chunk: ITerminalChunk): void {\n    if (chunk.text.indexOf('\\r') >= 0) {\n      throw new Error('StderrLineTransform expects chunks with normalized newlines');\n    }\n\n    // After _newlineNormalizerTransform was applied, we can now assume that all newlines\n    // use the \"\\n\" string\n    const text: string = chunk.text;\n    let startIndex: number = 0;\n\n    while (startIndex < text.length) {\n      if (chunk.kind === TerminalChunkKind.Stderr) {\n        this._accumulatedStderr = true;\n      }\n\n      const endIndex: number = text.indexOf('\\n', startIndex);\n      if (endIndex < 0) {\n        // we did not find \\n, so simply append\n        this._accumulatedLine += text.substring(startIndex);\n        break;\n      }\n\n      // append everything up to \\n\n      this._accumulatedLine += text.substring(startIndex, endIndex);\n\n      this._processAccumulatedLine();\n\n      // skip the \\n\n      startIndex = endIndex + 1;\n    }\n  }\n\n  protected onClose(): void {\n    if (this._accumulatedLine.length > 0) {\n      this._processAccumulatedLine();\n    }\n    this.autocloseDestination();\n  }\n\n  private _processAccumulatedLine(): void {\n    this._accumulatedLine += this.newline;\n\n    if (this._accumulatedStderr) {\n      this.destination.writeChunk({\n        kind: TerminalChunkKind.Stderr,\n        text: this._accumulatedLine\n      });\n    } else {\n      this.destination.writeChunk({\n        kind: TerminalChunkKind.Stdout,\n        text: this._accumulatedLine\n      });\n    }\n\n    this._accumulatedLine = '';\n    this._accumulatedStderr = false;\n  }\n}\n"]}