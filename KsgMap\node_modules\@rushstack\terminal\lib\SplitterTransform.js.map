{"version": 3, "file": "SplitterTransform.js", "sourceRoot": "", "sources": ["../src/SplitterTransform.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,yDAAqF;AAerF;;;;;;;;;;;GAWG;AACH,MAAa,iBAAkB,SAAQ,mCAAgB;IAGrD,YAAmB,OAAkC;QACnD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;IAChD,CAAC;IAES,YAAY,CAAC,KAAqB;QAC1C,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5C,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAES,OAAO;QACf,MAAM,MAAM,GAAY,EAAE,CAAC;QAE3B,2FAA2F;QAC3F,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5C,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC;gBAClC,IAAI,CAAC;oBACH,WAAW,CAAC,KAAK,EAAE,CAAC;gBACtB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC,KAAc,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;CACF;AAhCD,8CAgCC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport { TerminalWritable, type ITerminalWritableOptions } from './TerminalWritable';\nimport type { ITerminalChunk } from './ITerminalChunk';\n\n/**\n * Constructor options for {@link SplitterTransform}.\n *\n * @public\n */\nexport interface ISplitterTransformOptions extends ITerminalWritableOptions {\n  /**\n   * Each input chunk will be passed to each destination in the array.\n   */\n  destinations: TerminalWritable[];\n}\n\n/**\n * Use this instead of {@link TerminalTransform} if you need to output `ITerminalChunk`\n * data to more than one destination.\n *\n * @remarks\n *\n * Splitting streams complicates the pipeline topology and can make debugging more difficult.\n * For this reason, it is modeled as an explicit `SplitterTransform` node, rather than\n * as a built-in feature of `TerminalTransform`.\n *\n * @public\n */\nexport class SplitterTransform extends TerminalWritable {\n  public readonly destinations: ReadonlyArray<TerminalWritable>;\n\n  public constructor(options: ISplitterTransformOptions) {\n    super();\n    this.destinations = [...options.destinations];\n  }\n\n  protected onWriteChunk(chunk: ITerminalChunk): void {\n    for (const destination of this.destinations) {\n      destination.writeChunk(chunk);\n    }\n  }\n\n  protected onClose(): void {\n    const errors: Error[] = [];\n\n    // If an exception is thrown, try to ensure that the other destinations get closed properly\n    for (const destination of this.destinations) {\n      if (!destination.preventAutoclose) {\n        try {\n          destination.close();\n        } catch (error) {\n          errors.push(error as Error);\n        }\n      }\n    }\n\n    if (errors.length > 0) {\n      throw errors[0];\n    }\n  }\n}\n"]}