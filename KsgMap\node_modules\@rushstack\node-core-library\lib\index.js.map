{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,8CAA8C;AAE9C;;;;GAIG;AAEH,+DAA8D;AAArD,4HAAA,oBAAoB,OAAA;AAC7B,iCAOiB;AANf,8FAAA,KAAK,OAAA;AACL,mGAAA,UAAU,OAAA;AAOZ,yCAA6D;AAApD,0GAAA,aAAa,OAAA;AAAE,4GAAA,eAAe,OAAA;AACvC,+BAA8B;AAArB,4FAAA,IAAI,OAAA;AACb,mDAA0E;AAAjE,gHAAA,cAAc,OAAA;AACvB,2CAYsB;AADpB,wGAAA,UAAU,OAAA;AAEZ,yCAAkG;AAA/B,sGAAA,SAAS,OAAA;AAW5E,mCAQkB;AAPhB,gGAAA,MAAM,OAAA;AAQR,iDAAgD;AAAvC,8GAAA,aAAa,OAAA;AACtB,uCASoB;AANlB,sGAAA,UAAU,OAAA;AAKV,oGAAA,QAAQ,OAAA;AAEV,2CAUsB;AAFpB,wGAAA,UAAU,OAAA;AAGZ,uCAAsC;AAA7B,oGAAA,QAAQ,OAAA;AACjB,iDAAgD;AAAvC,8GAAA,aAAa,OAAA;AACtB,6CAA4C;AAAnC,0GAAA,WAAW,OAAA;AACpB,iDAAgD;AAAvC,8GAAA,aAAa,OAAA;AACtB,mDAAkF;AAAzE,gHAAA,cAAc,OAAA;AACvB,yDAA2F;AAA/C,sHAAA,iBAAiB,OAAA;AAC7D,6CAMuB;AALrB,0GAAA,WAAW,OAAA;AACX,gHAAA,iBAAiB,OAAA;AAKnB,+BAKgB;AAJd,4FAAA,IAAI,OAAA;AAKN,2DAA2G;AAAlG,gIAAA,0BAA0B,OAAA;AACnC,+BAAyF;AAAhF,gGAAA,QAAQ,OAAA;AAAE,4FAAA,IAAI,OAAA;AAAE,mGAAA,WAAW,OAAA;AACpC,+BAA8B;AAArB,4FAAA,IAAI,OAAA;AACb,2CAmBsB;AAlBpB,mHAAA,qBAAqB,OAAA;AACrB,wGAAA,UAAU,OAAA;AAkBZ,2CAAiE;AAAxD,wGAAA,UAAU,OAAA;AACnB,mDAAuE;AAA9D,gHAAA,cAAc,OAAA;AACvB,iDAAqE;AAA5D,8GAAA,aAAa,OAAA;AACtB,+DAAuF;AAArD,4HAAA,oBAAoB,OAAA;AACtD,uCAAsC;AAA7B,oGAAA,QAAQ,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See L<PERSON>EN<PERSON> in the project root for license information.\n\n/// <reference types=\"node\" preserve=\"true\" />\n\n/**\n * Core libraries that every NodeJS toolchain project should use.\n *\n * @packageDocumentation\n */\n\nexport { AlreadyReportedError } from './AlreadyReportedError';\nexport {\n  Async,\n  AsyncQueue,\n  type IAsyncParallelismOptions,\n  type IRunWithRetriesOptions,\n  type IRunWithTimeoutOptions,\n  type IWeighted\n} from './Async';\nexport type { Brand } from './PrimitiveTypes';\nexport { FileConstants, FolderConstants } from './Constants';\nexport { Enum } from './Enum';\nexport { EnvironmentMap, type IEnvironmentEntry } from './EnvironmentMap';\nexport {\n  type ExecutableStdioStreamMapping,\n  type ExecutableStdioMapping,\n  type IExecutableResolveOptions,\n  type IExecutableSpawnSyncOptions,\n  type IExecutableSpawnOptions,\n  type IWaitForExitOptions,\n  type IWaitForExitWithBufferOptions,\n  type IWaitForExitWithStringOptions,\n  type IWaitForExitResult,\n  type IProcessInfo,\n  Executable\n} from './Executable';\nexport { type IFileErrorOptions, type IFileErrorFormattingOptions, FileError } from './FileError';\nexport type {\n  INodePackageJson,\n  IPackageJson,\n  IPackageJsonDependencyTable,\n  IPackageJsonScriptTable,\n  IPackageJsonRepository,\n  IPeerDependenciesMetaTable,\n  IDependenciesMetaTable,\n  IPackageJsonExports\n} from './IPackageJson';\nexport {\n  Import,\n  type IImportResolveOptions,\n  type IImportResolveAsyncOptions,\n  type IImportResolveModuleOptions,\n  type IImportResolveModuleAsyncOptions,\n  type IImportResolvePackageOptions,\n  type IImportResolvePackageAsyncOptions\n} from './Import';\nexport { InternalError } from './InternalError';\nexport {\n  type JsonObject,\n  type JsonNull,\n  JsonSyntax,\n  type IJsonFileParseOptions,\n  type IJsonFileLoadAndValidateOptions,\n  type IJsonFileStringifyOptions,\n  type IJsonFileSaveOptions,\n  JsonFile\n} from './JsonFile';\nexport {\n  type IJsonSchemaErrorInfo,\n  type IJsonSchemaCustomFormat,\n  type IJsonSchemaFromFileOptions,\n  type IJsonSchemaFromObjectOptions,\n  type IJsonSchemaLoadOptions,\n  type IJsonSchemaValidateOptions,\n  type IJsonSchemaValidateObjectWithOptions,\n  JsonSchema,\n  type JsonSchemaVersion\n} from './JsonSchema';\nexport { LockFile } from './LockFile';\nexport { MapExtensions } from './MapExtensions';\nexport { MinimumHeap } from './MinimumHeap';\nexport { PosixModeBits } from './PosixModeBits';\nexport { ProtectableMap, type IProtectableMapParameters } from './ProtectableMap';\nexport { type IPackageJsonLookupParameters, PackageJsonLookup } from './PackageJsonLookup';\nexport {\n  PackageName,\n  PackageNameParser,\n  type IPackageNameParserOptions,\n  type IParsedPackageName,\n  type IParsedPackageNameOrError\n} from './PackageName';\nexport {\n  Path,\n  type FileLocationStyle,\n  type IPathFormatFileLocationOptions,\n  type IPathFormatConciselyOptions\n} from './Path';\nexport { RealNodeModulePathResolver, type IRealNodeModulePathResolverOptions } from './RealNodeModulePath';\nexport { Encoding, Text, NewlineKind, type IReadLinesFromIterableOptions } from './Text';\nexport { Sort } from './Sort';\nexport {\n  AlreadyExistsBehavior,\n  FileSystem,\n  type FileSystemCopyFilesAsyncFilter,\n  type FileSystemCopyFilesFilter,\n  type FolderItem,\n  type FileSystemStats,\n  type IFileSystemCopyFileBaseOptions,\n  type IFileSystemCopyFileOptions,\n  type IFileSystemCopyFilesAsyncOptions,\n  type IFileSystemCopyFilesOptions,\n  type IFileSystemCreateLinkOptions,\n  type IFileSystemDeleteFileOptions,\n  type IFileSystemMoveOptions,\n  type IFileSystemReadFileOptions,\n  type IFileSystemReadFolderOptions,\n  type IFileSystemUpdateTimeParameters,\n  type IFileSystemWriteBinaryFileOptions,\n  type IFileSystemWriteFileOptions\n} from './FileSystem';\nexport { FileWriter, type IFileWriterFlags } from './FileWriter';\nexport { LegacyAdapters, type LegacyCallback } from './LegacyAdapters';\nexport { StringBuilder, type IStringBuilder } from './StringBuilder';\nexport { type ISubprocessOptions, SubprocessTerminator } from './SubprocessTerminator';\nexport { TypeUuid } from './TypeUuid';\n"]}