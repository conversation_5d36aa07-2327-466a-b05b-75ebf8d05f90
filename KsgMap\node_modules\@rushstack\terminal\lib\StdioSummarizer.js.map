{"version": 3, "file": "StdioSummarizer.js", "sourceRoot": "", "sources": ["../src/StdioSummarizer.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,qDAA0E;AAC1E,yDAAqF;AAoBrF;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,MAAa,eAAgB,SAAQ,mCAAgB;IAYnD,YAAmB,OAAiC;QAClD,KAAK,CAAC,OAAO,CAAC,CAAC;QAJT,0BAAqB,GAAW,CAAC,CAAC;QAMxC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;QACpF,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;QAEvF,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACI,SAAS;QACd,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;QACnF,CAAC;QACD,MAAM,MAAM,GAAa,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpD,IAAI,IAAI,CAAC,qBAAqB,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,qBAAqB,oBAAoB,CAAC,CAAC;QACtE,CAAC;QACD,IAAI,IAAI,CAAC,qBAAqB,GAAG,CAAC,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,qBAAqB,qBAAqB,CAAC,CAAC;QACvE,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACvC,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACzB,CAAC;IAEM,YAAY,CAAC,KAAqB;QACvC,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAC1E,MAAM,IAAI,KAAK,CACb,+FAA+F;gBAC7F,kBAAkB;gBAClB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAC7B,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,kCAAiB,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACrE,2DAA2D;YAC3D,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;YACjC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;YAClC,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;QACjC,CAAC;aAAM,IAAI,IAAI,CAAC,eAAe,IAAI,KAAK,CAAC,IAAI,KAAK,kCAAiB,CAAC,MAAM,EAAE,CAAC;YAC3E,0DAA0D;YAC1D,OAAO;QACT,CAAC;QAED,uCAAuC;QACvC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACtD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACvC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAExC,yDAAyD;QACzD,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC3D,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;YAC/B,EAAE,IAAI,CAAC,qBAAqB,CAAC;QAC/B,CAAC;IACH,CAAC;CACF;AAlFD,0CAkFC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\n\nimport { type ITerminalChunk, TerminalChunkKind } from './ITerminalChunk';\nimport { type ITerminalWritableOptions, TerminalWritable } from './TerminalWritable';\n\n/**\n * Constructor options for {@link StdioSummarizer}.\n * @beta\n */\nexport interface IStdioSummarizerOptions extends ITerminalWritableOptions {\n  /**\n   * Specifies the maximum number of leading lines to include in the summary.\n   * @defaultValue `10`\n   */\n  leadingLines?: number;\n\n  /**\n   * Specifies the maximum number of trailing lines to include in the summary.\n   * @defaultValue `10`\n   */\n  trailingLines?: number;\n}\n\n/**\n * Summarizes the results of a failed build task by returning a subset of `stderr` output not to exceed\n * a specified maximum number of lines.\n *\n * @remarks\n * IMPORTANT: This transform assumes that its input was prepared by {@link StderrLineTransform}, so that each\n * {@link ITerminalChunk.text} item is a single line terminated by a `\"\\n\"` character.\n *\n * The {@link IStdioSummarizerOptions.leadingLines} and {@link IStdioSummarizerOptions.trailingLines}\n * counts specify the maximum number of lines to be returned. Any additional lines will be omitted.\n * For example, if `leadingLines` and `trailingLines` were set to `3`, then the summary of 16 `stderr` lines might\n * look like this:\n *\n * ```\n * Line 1\n * Line 2\n * Line 3\n *   ...10 lines omitted...\n * Line 14\n * Line 15\n * Line 16\n * ```\n *\n * If the `stderr` output is completely empty, then the `stdout` output will be summarized instead.\n *\n * @beta\n */\nexport class StdioSummarizer extends TerminalWritable {\n  // Capture up to this many leading lines\n  private _leadingLines: number;\n\n  // Capture up to this many trailing lines\n  private _trailingLines: number;\n\n  private readonly _abridgedLeading: string[];\n  private readonly _abridgedTrailing: string[];\n  private _abridgedOmittedLines: number = 0;\n  private _abridgedStderr: boolean;\n\n  public constructor(options?: IStdioSummarizerOptions) {\n    super(options);\n\n    if (!options) {\n      options = {};\n    }\n\n    this._leadingLines = options.leadingLines !== undefined ? options.leadingLines : 10;\n    this._trailingLines = options.trailingLines !== undefined ? options.trailingLines : 10;\n\n    this._abridgedLeading = [];\n    this._abridgedTrailing = [];\n    this._abridgedStderr = false;\n  }\n\n  /**\n   * Returns the summary report.\n   *\n   * @remarks\n   * The `close()` method must be called before `getReport()` can be used.\n   */\n  public getReport(): string {\n    if (this.isOpen) {\n      throw new Error('The summary cannot be prepared until after close() is called.');\n    }\n    const report: string[] = [...this._abridgedLeading];\n    if (this._abridgedOmittedLines === 1) {\n      report.push(`  ...${this._abridgedOmittedLines} line omitted...\\n`);\n    }\n    if (this._abridgedOmittedLines > 1) {\n      report.push(`  ...${this._abridgedOmittedLines} lines omitted...\\n`);\n    }\n    report.push(...this._abridgedTrailing);\n    return report.join('');\n  }\n\n  public onWriteChunk(chunk: ITerminalChunk): void {\n    if (chunk.text.length === 0 || chunk.text[chunk.text.length - 1] !== '\\n') {\n      throw new Error(\n        'StdioSummarizer expects chunks that were separated parsed into lines by StderrLineTransform\\n' +\n          ' Invalid input: ' +\n          JSON.stringify(chunk.text)\n      );\n    }\n\n    if (chunk.kind === TerminalChunkKind.Stderr && !this._abridgedStderr) {\n      // The first time we see stderr, switch to capturing stderr\n      this._abridgedStderr = true;\n      this._abridgedLeading.length = 0;\n      this._abridgedTrailing.length = 0;\n      this._abridgedOmittedLines = 0;\n    } else if (this._abridgedStderr && chunk.kind !== TerminalChunkKind.Stderr) {\n      // If we're capturing stderr, then ignore non-stderr input\n      return;\n    }\n\n    // Did we capture enough leading lines?\n    if (this._abridgedLeading.length < this._leadingLines) {\n      this._abridgedLeading.push(chunk.text);\n      return;\n    }\n\n    this._abridgedTrailing.push(chunk.text);\n\n    // If we captured to many trailing lines, omit the extras\n    while (this._abridgedTrailing.length > this._trailingLines) {\n      this._abridgedTrailing.shift();\n      ++this._abridgedOmittedLines;\n    }\n  }\n}\n"]}